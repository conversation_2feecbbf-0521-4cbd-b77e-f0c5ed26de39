using BepInEx;
using BepInEx.Logging;
using UnityEngine;
using System;
using System.Reflection;

[BepInPlugin("com.safecoin.blockpost", "Safe Coin Mod", "1.0.0")]
public class SafeCoinMod : BaseUnityPlugin
{
    private ManualLogSource logger;
    private bool coinsAdded = false; // لضمان إضافة الكوينز مرة واحدة فقط

    void Awake()
    {
        logger = Logger;
        logger.LogInfo("Safe Coin Mod تم تحميله");
        logger.LogInfo("اضغط F9 لإضافة 1000 كوين مرة واحدة فقط");
    }

    void Update()
    {
        // استخدام F9 بدلاً من F1 لتجنب التعارض
        if (Input.GetKeyDown(KeyCode.F9) && !coinsAdded)
        {
            AddCoinsOnce();
        }
    }

    private void AddCoinsOnce()
    {
        try
        {
            logger.LogInfo("محاولة إضافة 1000 كوين بأمان...");
            
            // البحث بحذر عن متغيرات الكوينز فقط
            var allObjects = FindObjectsOfType<MonoBehaviour>();
            bool success = false;
            
            foreach (var obj in allObjects)
            {
                if (obj == null) continue;
                
                var type = obj.GetType();
                if (type == null) continue;
                
                // البحث فقط في الكلاسات التي تحتوي على "Player" أو "Game" في الاسم
                string typeName = type.Name.ToLower();
                if (!typeName.Contains("player") && !typeName.Contains("game") && !typeName.Contains("manager"))
                    continue;
                
                var fields = type.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                
                foreach (var field in fields)
                {
                    if (field.FieldType != typeof(int)) continue;
                    
                    string fieldName = field.Name.ToLower();
                    
                    // البحث فقط عن الحقول التي تحتوي على "coin" أو "money"
                    if (fieldName.Contains("coin") || fieldName.Contains("money"))
                    {
                        try
                        {
                            int currentValue = (int)field.GetValue(obj);
                            
                            // التحقق من أن القيمة منطقية (بين 0 و 100000)
                            if (currentValue >= 0 && currentValue <= 100000)
                            {
                                // إضافة 1000 كوين فقط
                                field.SetValue(obj, currentValue + 1000);
                                
                                logger.LogInfo($"✅ تم إضافة 1000 كوين بنجاح!");
                                logger.LogInfo($"الرصيد السابق: {currentValue}");
                                logger.LogInfo($"الرصيد الجديد: {currentValue + 1000}");
                                
                                success = true;
                                coinsAdded = true; // منع الإضافة مرة أخرى
                                return; // الخروج فور النجاح
                            }
                        }
                        catch (Exception ex)
                        {
                            logger.LogWarning($"تعذر تعديل {field.Name}: {ex.Message}");
                        }
                    }
                }
            }
            
            if (!success)
            {
                logger.LogWarning("⚠️ لم يتم العثور على متغير الكوينز");
                logger.LogInfo("تأكد من أنك داخل اللعبة وليس في القائمة الرئيسية");
            }
        }
        catch (Exception ex)
        {
            logger.LogError($"❌ خطأ: {ex.Message}");
        }
    }

    void OnDestroy()
    {
        logger.LogInfo("Safe Coin Mod تم إلغاء تحميله");
    }
}
