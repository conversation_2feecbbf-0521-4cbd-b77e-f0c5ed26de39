{"version": 3, "targets": {".NETFramework,Version=v3.5": {}}, "libraries": {}, "projectFileDependencyGroups": {".NETFramework,Version=v3.5": ["Microsoft.NETFramework.ReferenceAssemblies >= 1.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\ty\\CoinHack.csproj", "projectName": "CoinHack", "projectPath": "C:\\Users\\<USER>\\Downloads\\ty\\CoinHack.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\ty\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net35"], "frameworks": {"net35": {"targetAlias": "net35", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net35": {"targetAlias": "net35", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.410\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1100", "level": "Error", "message": "Unable to resolve 'Microsoft.NETFramework.ReferenceAssemblies (>= 1.0.3)' for '.NETFramework,Version=v3.5'.", "libraryId": "Microsoft.NETFramework.ReferenceAssemblies", "targetGraphs": [".NETFramework,Version=v3.5"]}]}