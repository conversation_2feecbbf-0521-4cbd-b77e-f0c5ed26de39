using BepInEx;
using BepInEx.Logging;
using UnityEngine;
using System;
using System.Reflection;

[BepInPlugin("com.coinhack.blockpost", "Blockpost Coin Hack", "1.0.0")]
public class SimpleCoinHack : BaseUnityPlugin
{
    private ManualLogSource logger;

    void Awake()
    {
        logger = Logger;
        logger.LogInfo("Blockpost Coin Hack تم تحميله بنجاح!");
        logger.LogInfo("اضغط F1 لإضافة 1000 كوين");
    }

    void Update()
    {
        if (Input.GetKeyDown(KeyCode.F1))
        {
            AddCoins();
        }
    }

    private void AddCoins()
    {
        try
        {
            logger.LogInfo("محاولة إضافة 1000 كوين...");
            
            var allObjects = FindObjectsOfType<MonoBehaviour>();
            bool found = false;
            
            foreach (var obj in allObjects)
            {
                var type = obj.GetType();
                var fields = type.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                
                foreach (var field in fields)
                {
                    if (field.FieldType == typeof(int))
                    {
                        string fieldName = field.Name.ToLower();
                        if (fieldName.Contains("coin") || fieldName.Contains("money") || 
                            fieldName.Contains("currency") || fieldName.Contains("balance"))
                        {
                            try
                            {
                                int currentValue = (int)field.GetValue(obj);
                                if (currentValue >= 0 && currentValue < 999999)
                                {
                                    field.SetValue(obj, currentValue + 1000);
                                    logger.LogInfo($"تم إضافة 1000 كوين إلى {field.Name} في {type.Name}");
                                    logger.LogInfo($"الرصيد: {currentValue} -> {currentValue + 1000}");
                                    found = true;
                                }
                            }
                            catch (Exception ex)
                            {
                                logger.LogWarning($"خطأ في تعديل {field.Name}: {ex.Message}");
                            }
                        }
                    }
                }
            }
            
            if (!found)
            {
                logger.LogWarning("لم يتم العثور على متغيرات الكوينز");
            }
        }
        catch (Exception ex)
        {
            logger.LogError($"خطأ عام: {ex.Message}");
        }
    }
}
