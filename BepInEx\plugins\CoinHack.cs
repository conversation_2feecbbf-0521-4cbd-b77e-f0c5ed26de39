using BepInEx;
using BepInEx.Configuration;
using BepInEx.Logging;
using HarmonyLib;
using UnityEngine;
using System;
using System.Reflection;

namespace CoinHack
{
    [BepInPlugin(PluginGUID, PluginName, PluginVersion)]
    public class CoinHackPlugin : BaseUnityPlugin
    {
        public const string PluginGUID = "com.coinhack.blockpost";
        public const string PluginName = "Blockpost Coin Hack";
        public const string PluginVersion = "1.0.0";

        private static ManualLogSource logger;
        private static ConfigEntry<KeyCode> addCoinsKey;
        private static ConfigEntry<int> coinsToAdd;
        private Harmony harmony;

        void Awake()
        {
            logger = Logger;
            
            // إعداد الكونفيغ
            addCoinsKey = Config.Bind("General", "AddCoinsKey", KeyCode.F1, "المفتاح لإضافة الكوينز");
            coinsToAdd = Config.Bind("General", "CoinsToAdd", 1000, "عدد الكوينز المراد إضافتها");

            // تطبيق الهارموني باتشز
            harmony = new Harmony(PluginGUID);
            harmony.PatchAll();

            logger.LogInfo($"Plugin {PluginName} loaded successfully!");
            logger.LogInfo($"اضغط {addCoinsKey.Value} لإضافة {coinsToAdd.Value} كوين");
        }

        void Update()
        {
            // التحقق من الضغط على المفتاح
            if (Input.GetKeyDown(addCoinsKey.Value))
            {
                AddCoins();
            }
        }

        private void AddCoins()
        {
            try
            {
                // البحث عن كلاس إدارة اللاعب أو النقود
                var playerManagerType = FindTypeByName("PlayerManager") ?? 
                                       FindTypeByName("GameManager") ?? 
                                       FindTypeByName("CoinManager") ??
                                       FindTypeByName("MoneyManager");

                if (playerManagerType != null)
                {
                    var instance = FindObjectOfType(playerManagerType);
                    if (instance != null)
                    {
                        // البحث عن متغير الكوينز
                        var coinField = playerManagerType.GetField("coins", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance) ??
                                       playerManagerType.GetField("money", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance) ??
                                       playerManagerType.GetField("currency", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance) ??
                                       playerManagerType.GetField("balance", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

                        if (coinField != null)
                        {
                            var currentCoins = (int)coinField.GetValue(instance);
                            coinField.SetValue(instance, currentCoins + coinsToAdd.Value);
                            logger.LogInfo($"تم إضافة {coinsToAdd.Value} كوين! الرصيد الحالي: {currentCoins + coinsToAdd.Value}");
                            return;
                        }

                        // البحث عن دالة إضافة الكوينز
                        var addCoinMethod = playerManagerType.GetMethod("AddCoins", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance) ??
                                           playerManagerType.GetMethod("AddMoney", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance) ??
                                           playerManagerType.GetMethod("AddCurrency", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

                        if (addCoinMethod != null)
                        {
                            addCoinMethod.Invoke(instance, new object[] { coinsToAdd.Value });
                            logger.LogInfo($"تم إضافة {coinsToAdd.Value} كوين باستخدام الدالة!");
                            return;
                        }
                    }
                }

                // طريقة بديلة: البحث في جميع الكائنات
                SearchAndModifyCoins();
            }
            catch (Exception ex)
            {
                logger.LogError($"خطأ في إضافة الكوينز: {ex.Message}");
            }
        }

        private void SearchAndModifyCoins()
        {
            try
            {
                // البحث في جميع الكائنات النشطة
                var allObjects = FindObjectsOfType<MonoBehaviour>();
                
                foreach (var obj in allObjects)
                {
                    var type = obj.GetType();
                    
                    // البحث عن متغيرات الكوينز
                    var fields = type.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    
                    foreach (var field in fields)
                    {
                        if (field.FieldType == typeof(int) && 
                            (field.Name.ToLower().Contains("coin") || 
                             field.Name.ToLower().Contains("money") ||
                             field.Name.ToLower().Contains("currency") ||
                             field.Name.ToLower().Contains("balance")))
                        {
                            var currentValue = (int)field.GetValue(obj);
                            if (currentValue >= 0 && currentValue < 1000000) // تحقق معقول من القيمة
                            {
                                field.SetValue(obj, currentValue + coinsToAdd.Value);
                                logger.LogInfo($"تم تعديل {field.Name} في {type.Name}: {currentValue} -> {currentValue + coinsToAdd.Value}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"خطأ في البحث عن الكوينز: {ex.Message}");
            }
        }

        private Type FindTypeByName(string typeName)
        {
            try
            {
                foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
                {
                    foreach (var type in assembly.GetTypes())
                    {
                        if (type.Name.Contains(typeName))
                        {
                            return type;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning($"خطأ في البحث عن النوع {typeName}: {ex.Message}");
            }
            return null;
        }

        void OnDestroy()
        {
            harmony?.UnpatchSelf();
        }
    }

    // باتش لاعتراض دوال الكوينز
    [HarmonyPatch]
    public class CoinPatches
    {
        private static ManualLogSource Logger => CoinHackPlugin.logger;

        // باتش عام لأي دالة تحتوي على "coin" في اسمها
        [HarmonyPatch(typeof(MonoBehaviour))]
        [HarmonyPatch("*")]
        [HarmonyPrefix]
        public static void CoinMethodPrefix(MonoBehaviour __instance, MethodBase __originalMethod)
        {
            try
            {
                if (__originalMethod.Name.ToLower().Contains("coin") ||
                    __originalMethod.Name.ToLower().Contains("money") ||
                    __originalMethod.Name.ToLower().Contains("currency"))
                {
                    Logger?.LogInfo($"تم اعتراض دالة: {__instance.GetType().Name}.{__originalMethod.Name}");
                }
            }
            catch (Exception ex)
            {
                Logger?.LogWarning($"خطأ في الباتش: {ex.Message}");
            }
        }
    }
}
