using BepInEx;
using BepInEx.Configuration;
using BepInEx.Logging;
using UnityEngine;
using System;
using System.Reflection;

namespace CoinHack
{
    [BepInPlugin(PluginGUID, PluginName, PluginVersion)]
    public class CoinHackPlugin : BaseUnityPlugin
    {
        public const string PluginGUID = "com.coinhack.blockpost";
        public const string PluginName = "Blockpost Coin Hack";
        public const string PluginVersion = "1.0.0";

        private static ManualLogSource logger;
        private static ConfigEntry<KeyCode> addCoinsKey;
        private static ConfigEntry<int> coinsToAdd;

        void Awake()
        {
            logger = Logger;

            // إعداد الكونفيغ
            addCoinsKey = Config.Bind("General", "AddCoinsKey", KeyCode.F1, "المفتاح لإضافة الكوينز");
            coinsToAdd = Config.Bind("General", "CoinsToAdd", 1000, "عدد الكوينز المراد إضافتها");

            logger.LogInfo($"Plugin {PluginName} loaded successfully!");
            logger.LogInfo($"اضغط {addCoinsKey.Value} لإضافة {coinsToAdd.Value} كوين");
        }

        void Update()
        {
            // التحقق من الضغط على المفتاح
            if (Input.GetKeyDown(addCoinsKey.Value))
            {
                AddCoins();
            }
        }

        private void AddCoins()
        {
            try
            {
                logger.LogInfo("محاولة إضافة الكوينز...");

                // البحث في جميع الكائنات النشطة
                var allObjects = FindObjectsOfType<MonoBehaviour>();
                bool foundCoin = false;

                foreach (var obj in allObjects)
                {
                    var type = obj.GetType();
                    var fields = type.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

                    foreach (var field in fields)
                    {
                        if (field.FieldType == typeof(int) &&
                            (field.Name.ToLower().Contains("coin") ||
                             field.Name.ToLower().Contains("money") ||
                             field.Name.ToLower().Contains("currency") ||
                             field.Name.ToLower().Contains("balance")))
                        {
                            var currentValue = (int)field.GetValue(obj);
                            if (currentValue >= 0 && currentValue < 1000000)
                            {
                                field.SetValue(obj, currentValue + coinsToAdd.Value);
                                logger.LogInfo($"تم تعديل {field.Name} في {type.Name}: {currentValue} -> {currentValue + coinsToAdd.Value}");
                                foundCoin = true;
                            }
                        }
                    }
                }

                if (!foundCoin)
                {
                    logger.LogWarning("لم يتم العثور على متغيرات الكوينز");
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"خطأ في إضافة الكوينز: {ex.Message}");
            }
        }

        void OnDestroy()
        {
            logger.LogInfo("تم إلغاء تحميل Plugin");
        }
    }
}
