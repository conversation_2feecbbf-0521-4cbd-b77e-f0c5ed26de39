@echo off
echo Building CoinHack.dll...

REM Set paths
set DOTNET_PATH="C:\Program Files\dotnet\dotnet.exe"
set FRAMEWORK_PATH="C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.8"
set BEPINEX_PATH="BepInEx\core\BepInEx.Core.dll"
set UNITY_PATH="BepInEx\unity-libs\UnityEngine.dll"
set UNITY_CORE_PATH="BepInEx\unity-libs\UnityEngine.CoreModule.dll"

REM Try different compilation methods
echo Trying method 1: dotnet build...
%DOTNET_PATH% build CoinHack.csproj -c Release -v minimal

echo.
echo Trying method 2: MSBuild...
"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" CoinHack.csproj /p:Configuration=Release /v:minimal

echo.
echo Trying method 3: csc from .NET Framework...
"C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe" /target:library /reference:%BEPINEX_PATH% /reference:%UNITY_PATH% /reference:%UNITY_CORE_PATH% /out:"BepInEx\plugins\CoinHack.dll" "SimpleCoinHack.cs"

echo.
echo Build complete. Checking output...
dir "BepInEx\plugins\*.dll"

pause
