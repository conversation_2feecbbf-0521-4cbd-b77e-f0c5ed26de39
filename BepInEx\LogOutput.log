[Message: Preloader] BepInEx 6.0.0-pre.1 - blockpost
[Info   :   BepInEx] System platform: Windows 10 64-bit
[Info   :   BepInEx] Process bitness: 32-bit (x86)
[Info   : Preloader] Running under Unity v2018.4.32
[Info   : Preloader] 0 patcher plugins loaded
[Info   : Preloader] 0 assemblies discovered
[Message:AssemblyPatcher] Executing 0 patch(es)
[Message:   BepInEx] Chainloader initialized
[Info   :Unhollower] Registered mono type UnhollowerRuntimeLib.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[Info   :   BepInEx] 1 plugin to load
[Info   :   BepInEx] Loading [BRHC 1.0.1]
[Info   :      BRHC] Plugin BRHC is loaded!
[Info   :Unhollower] Registered mono type BHRCHACK.ExecuteCheat in il2cpp domain
[Message:      BRHC] Registered hack class type into il2cpp!
[Message:      BRHC] Created new hack GameObject!
[Message:      BRHC] Patched game functions with hack functions!
[Message:   BepInEx] Chainloader startup complete
